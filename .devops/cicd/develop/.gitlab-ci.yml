stages:
  - deploy

#### DEPLOY ####
deploy:
  stage: deploy
  needs: []
  variables:
    CONTAINER_NAME: wap_portal_cms_dev
    DOCKER_IMAGE_NAME: wap/portal-cms:dev
  before_script:
    # Clean up any existing container and image
    - docker rm -f $CONTAINER_NAME || true
    - docker image rm $DOCKER_IMAGE_NAME || true
  script:
    - cp $DEV_ENV_FILE ./.env
    - cp $GOOGLE_CREDENTIAL ./credentials.json
    - docker build -f .devops/docker/Dockerfile -t $DOCKER_IMAGE_NAME .
    - docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $DEV_PORT:3000 $DOCKER_IMAGE_NAME
    - rm .env
  tags:
    - wap-portal-cms-runner
