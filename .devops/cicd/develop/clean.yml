cleanup:
  stage: clean
  script:
    - echo ">>>>>>>>> CLEANING on gitlab server..."
    - ls
    - pwd
    - rm -rf ./*
    - docker system prune -af
    - echo ">>>>>>>>> CLEAN on gitlab server DONE"
    - echo ">>>>>>>>> CLEANING on app server..."
    - ssh $SERVER_DEV "docker --version"
    - ssh $SERVER_DEV 'docker system prune -af'
    - echo ">>>>>>>>> CLEANING on app server DONE"
  tags:
    - wap-portal-cms-runner
