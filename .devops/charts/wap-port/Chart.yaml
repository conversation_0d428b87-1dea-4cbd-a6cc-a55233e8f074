apiVersion: v2
name: wap-port
version: 2.0.0-rc.2
kubeVersion: ">=1.30.0 <=1.33.0"
description: wap-port is a digital platform where users can access and manage information related to medical.
type: application
keywords:
- ui
- web
- medical
- application
dependencies: # A list of the chart requirements (optional)
  - name: common
    version: 0.x.x
    repository: oci://registry-1.docker.io/jacger
    tags:
      - common
      - library
maintainers: # (optional)
  - name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
    url: https://github.com/jacger
icon: https://minastik.com/wp-content/themes/hello-theme-child-master/assets/images/minastik-full-ver.png
appVersion: 1.26.0  # This is consider versions of this helm templates, the version of app will specific by "helm install `version`"
deprecated: false
annotations:
  category: Application