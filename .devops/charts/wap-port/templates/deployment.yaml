apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.common.name }}
  namespace: {{ .Values.common.namespace }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
  revisionHistoryLimit: 10
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
      annotations:
        rollme: {{ randAlphaNum 5 | quote }}
    spec:
      imagePullSecrets:
        - name: {{ .Values.image.imagePullSecrets }}harbor-wap-port-registry-token
      containers:
        - name: {{ .Values.common.name }}
          image: {{ .Values.image.name }}
          imagePullPolicy: "Always"
          ports:
            - name: http
              containerPort: 80
            - name: https
              containerPort: 443
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 180
            periodSeconds: 20
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          resources:
            requests:
              cpu: 1
              memory: 512Mi
            limits:
              cpu: 1
              memory: 1024Mi