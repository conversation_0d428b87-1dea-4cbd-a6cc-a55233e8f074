apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.common.name }}
  namespace: {{ .Values.common.namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
spec:
  ingressClassName: nginx
  rules:
    - host: {{ .Values.ingress.hostname }}
      http:
        paths:
          - path: {{ .Values.ingress.path }}
            pathType: {{ .Values.ingress.pathType }}
            backend:
              service:
                name: {{ .Values.common.name }}
                port:
                  name: http