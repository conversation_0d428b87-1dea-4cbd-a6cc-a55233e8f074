{{/* vim: set filetype=mustache: */}}
{{/*
Return the proper wap-port image name
*/}}
{{- define "wap-port.image" -}}
{{- include "common.images.image" (dict "imageRoot" .Values.image "global" .Values.global) -}}
{{- end -}}

{{/*
Return the proper wap-port Docker Image Registry Secret Names
*/}}
{{- define "wap-port.imagePullSecrets" -}}
{{- include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) -}}
{{- end -}}

{{/*
Return true if mouting a static web page
*/}}
{{- define "wap-port.useHtdocs" -}}
{{ default "" (or .Values.cloneHtdocsFromGit.enabled .Values.htdocsConfigMap .Values.htdocsPVC) }}
{{- end -}}

{{/*
Return associated volume
*/}}
{{- define "wap-port.htdocsVolume" -}}
{{- if .Values.cloneHtdocsFromGit.enabled }}
emptyDir: {}
{{- else if .Values.htdocsConfigMap }}
configMap:
  name: {{ .Values.htdocsConfigMap }}
{{- else if .Values.htdocsPVC }}
persistentVolumeClaim:
  claimName: {{ .Values.htdocsPVC }}
{{- end }}
{{- end -}}

{{/*
Validate data
*/}}
{{- define "wap-port.validateValues" -}}
{{- $messages := list -}}
{{- $messages := append $messages (include "wap-port.validateValues.htdocs" .) -}}
{{- $messages := append $messages (include "wap-port.validateValues.htdocsGit" .) -}}
{{- $messages := without $messages "" -}}
{{- $message := join "\n" $messages -}}
 {{- if $message -}}
{{-   printf "\nVALUES VALIDATION:\n%s" $message | fail -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "wap-port.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Validate data (htdocs)
*/}}
{{- define "wap-port.validateValues.htdocs" -}}
{{- if or (and .Values.cloneHtdocsFromGit.enabled (or .Values.htdocsPVC .htdocsConfigMap )) (and .Values.htdocsPVC (or .Values.htdocsConfigMap .Values.cloneHtdocsFromGit.enabled )) (and .Values.htdocsConfigMap (or .Values.htdocsPVC .Values.cloneHtdocsFromGit.enabled )) }}
wap-port: htdocs
    You have selected more than one way of deploying htdocs. Please select only one of htdocsConfigMap cloneHtdocsFromGit or htdocsVolume
{{- end }}
{{- end -}}

{{/*
Validate data (htdocs git)
*/}}
{{- define "wap-port.validateValues.htdocsGit" -}}
{{- if .Values.cloneHtdocsFromGit.enabled }}
  {{- if not .Values.cloneHtdocsFromGit.repository }}
wap-port: htdocs-git-repository
    You did not specify a git repository to clone. Please set cloneHtdocsFromGit.repository
  {{- end }}
  {{- if not .Values.cloneHtdocsFromGit.branch }}
wap-port: htdocs-git-branch
    You did not specify a branch to checkout in the git repository. Please set cloneHtdocsFromGit.branch
  {{- end }}
{{- end -}}
{{- end -}}

{{/*
Validate values of wap-port - Incorrect extra volume settings
*/}}
{{- define "wap-port.validateValues.extraVolumes" -}}
{{- if and (.Values.extraVolumes) (not (or .Values.extraVolumeMounts .Values.cloneHtdocsFromGit.extraVolumeMounts)) -}}
wap-port: missing-extra-volume-mounts
    You specified extra volumes but not mount points for them. Please set
    the extraVolumeMounts value
{{- end -}}
{{- end -}}

{{/*
Return the proper git image name
*/}}
{{- define "git.image" -}}
{{- include "common.images.image" (dict "imageRoot" .Values.git "global" .Values.global) -}}
{{- end -}}

{{/*
Get the vhosts config map name.
*/}}
{{- define "wap-port.vhostsConfigMap" -}}
{{- if .Values.vhostsConfigMap -}}
    {{- printf "%s" (tpl .Values.vhostsConfigMap $) -}}
{{- else -}}
    {{- printf "%s-vhosts" (include "common.names.fullname" . ) | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{/*
Get the httpd.conf config map name.
*/}}
{{- define "wap-port.httpdConfConfigMap" -}}
{{- if .Values.httpdConfConfigMap -}}
    {{- printf "%s" (tpl .Values.httpdConfConfigMap $) -}}
{{- else -}}
    {{- printf "%s-httpd-conf" (include "common.names.fullname" . ) | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
