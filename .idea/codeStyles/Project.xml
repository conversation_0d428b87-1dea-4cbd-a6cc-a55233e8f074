<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <DBN-PSQL>
      <case-options enabled="true">
        <option name="KEYWORD_CASE" value="lower" />
        <option name="FUNCTION_CASE" value="lower" />
        <option name="PARAMETER_CASE" value="lower" />
        <option name="DATATYPE_CASE" value="lower" />
        <option name="OBJECT_CASE" value="preserve" />
      </case-options>
      <formatting-settings enabled="false" />
    </DBN-PSQL>
    <DBN-SQL>
      <case-options enabled="true">
        <option name="KEYWORD_CASE" value="lower" />
        <option name="FUNCTION_CASE" value="lower" />
        <option name="PARAMETER_CASE" value="lower" />
        <option name="DATATYPE_CASE" value="lower" />
        <option name="OBJECT_CASE" value="preserve" />
      </case-options>
      <formatting-settings enabled="false">
        <option name="STATEMENT_SPACING" value="one_line" />
        <option name="CLAUSE_CHOP_DOWN" value="chop_down_if_statement_long" />
        <option name="ITERATION_ELEMENTS_WRAPPING" value="chop_down_if_not_single" />
      </formatting-settings>
    </DBN-SQL>
    <HTMLCodeStyleSettings>
      <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
    </HTMLCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="false" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="false" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACKETS" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="SPACES_WITHIN_INTERPOLATION_EXPRESSIONS" value="true" />
      <option name="IMPORT_PREFER_ABSOLUTE_PATH" value="TRUE" />
    </TypeScriptCodeStyleSettings>
    <VueCodeStyleSettings>
      <option name="INTERPOLATION_NEW_LINE_AFTER_START_DELIMITER" value="false" />
      <option name="INTERPOLATION_NEW_LINE_BEFORE_END_DELIMITER" value="false" />
    </VueCodeStyleSettings>
    <codeStyleSettings language="HTML">
      <option name="SOFT_MARGINS" value="100" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="SOFT_MARGINS" value="100" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="SOFT_MARGINS" value="100" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="Vue">
      <option name="SOFT_MARGINS" value="100" />
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>