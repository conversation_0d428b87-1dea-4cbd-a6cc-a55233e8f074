import { checkSubscription } from '@/collections/Users/<USER>/checkSubscription'
import type { AccessArgs } from 'payload'
import type { User } from '../payload-types'

type isAdminOrAdvancedAccess = (args: AccessArgs<User>) => Promise<boolean>

export const adminOrAdvancedAccess: isAdminOrAdvancedAccess = async ({
  req: { user, payload },
}) => {
  return checkSubscription(['trial', 'advanced'], user, payload)
}
