import { checkRole } from '@/collections/Users/<USER>/checkRole'
import { User } from '@/payload-types'
import { AccessArgs } from 'payload'

type isAdminOrCreatedBy = (args: AccessArgs<User>) =>
  | boolean
  | {
      'user.id': {
        equals: any
      }
    }
export const adminOrCreatedBy: isAdminOrCreatedBy = ({ req: { user } }) => {
  //Check if user has the 'admin' role
  if (user && checkRole(['admin'], user)) {
    return true
  }
  //Allow only documents with the current user set to the 'user' field
  if (user) {
    return {
      'user.id': {
        equals: user.id,
      },
    }
  }

  return false
}
