import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { checkRole } from '@/collections/Users/<USER>/checkRole'
import { User } from '@/payload-types'
import { AccessArgs } from 'payload'

type isAdminOrCurrentUser = (args: AccessArgs<User>) => Promise<boolean>

export const adminOrCurrentUser: isAdminOrCurrentUser = async ({ req, id }) => {
  const { user } = req

  // Admin check
  if (user && checkRole(['admin'], user)) {
    return true
  }

  // Current user check with session validation
  if (user?.id === id) {
    const isValidSession = await authenticatedWithValidSession({ req })
    return isValidSession
  }

  return false
}
