import { EXCEPTION_STATUS } from '@/constants/exceptionStatus.constant'
import { LOGIN_SESSION_COOKIE_KEY } from '@/constants/global.constant'
import { X_LOGIN_SESSION } from '@/constants/storageKeys.constant'
import type { User } from '@/payload-types'
import { parse } from 'cookie'
import jwt from 'jsonwebtoken'
import { userAgent } from 'next/server'
import { APIError, PayloadRequest, type AccessArgs } from 'payload'

type isAuthenticated = (args: AccessArgs<User>, skipAdmin?: boolean) => Promise<boolean>

// check if this is an admin request
function isAdminRequest(req: PayloadRequest): boolean {
  // Check various properties that might indicate an admin request
  return Boolean(
    req.pathname?.includes('/admin') ||
      req.url?.includes('/admin') ||
      req.origin?.includes('/admin') ||
      req.headers?.get('referer')?.includes('/admin'),
  )
}

// create login session errors
function createSessionError(message: string, statusCode = EXCEPTION_STATUS.SESSION_INVALID.code) {
  return new APIError(message, statusCode, { sessionInvalid: true }, true)
}

// handle authentication failures based on request source
function handleAuthFailure(
  req: any,
  message: string,
  statusCode = EXCEPTION_STATUS.SESSION_INVALID.code,
) {
  // Check if this is an admin request
  if (isAdminRequest(req)) {
    // For admin requests, just return false without throwing
    return false
  }

  // For non-admin requests, throw the error
  throw createSessionError(message, statusCode)
}

export const authenticatedWithValidSession: isAuthenticated = async (
  { req }: AccessArgs<User>,
  skipAdmin = true,
) => {
  // return true
  if (!req?.user) {
    return false
  }
  const { user } = req

  if (skipAdmin && user.roles?.includes('admin')) return true

  const { device } = userAgent(req)
  const currentDeviceType = device.type === 'mobile' ? 'mobile' : 'desktop'

  // Get cookie header and parse it properly
  const cookieHeader = req.headers?.get('cookie')

  // Parse cookies using the cookie library for more reliable extraction
  const cookies = cookieHeader ? parse(cookieHeader) : {}

  // Sessin ID in header
  const customSessionHeader = req.headers[X_LOGIN_SESSION] || req.headers.get(X_LOGIN_SESSION)

  // Retrieve session token from either cookies (web app) or custom header (mobile app via WebView)
  const sessionToken = cookies[LOGIN_SESSION_COOKIE_KEY] || customSessionHeader
  if (!sessionToken) {
    return handleAuthFailure(req, 'MES-448')
  }

  try {
    // Verify and decode the JWT token
    const decodedToken = jwt.verify(sessionToken, req.payload.secret) as {
      sessionId: string
      exp: number
      isMobileLogin?: boolean
    }

    // TEMPORARY: For mobile login, we don't need to check the session
    if (decodedToken.isMobileLogin) {
      return true
    }

    // Check if token has expired
    if (decodedToken.exp < Math.floor(Date.now() / 1000)) {
      return handleAuthFailure(req, 'MES-449')
    }

    // Extract the session ID from the decoded token
    const sessionId = decodedToken.sessionId

    // Find session by ID
    const session = await req.payload.findByID({
      collection: 'login-sessions',
      id: sessionId,
    })

    // Verify the session belongs to the current user, is active, and matches device type
    if (
      !session ||
      !session.isActive ||
      (session.user as User)?.id !== user.id ||
      session.deviceType !== currentDeviceType
    ) {
      return handleAuthFailure(req, 'MES-450')
    }

    // Double-check if session has expired (if you have an expiresAt field)
    if (session.expiredAt && new Date(session.expiredAt) < new Date()) {
      return handleAuthFailure(req, 'MES-451')
    }

    return true
  } catch (error) {
    console.error('Lỗi xác thực phiên đăng nhập:', error)

    // For admin requests, return false instead of throwing
    if (isAdminRequest(req)) {
      return false
    }

    // For other requests, throw the appropriate error
    if (error instanceof APIError && error.data?.sessionInvalid) {
      throw error
    }
    throw createSessionError('Xác thực phiên đăng nhập không thành công')
  }
}
