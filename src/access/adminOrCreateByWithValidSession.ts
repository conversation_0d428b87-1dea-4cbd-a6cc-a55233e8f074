import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { checkRole } from '@/collections/Users/<USER>/checkRole'
import { User } from '@/payload-types'
import { AccessArgs } from 'payload'

type isAdminOrCreateByWithValidSession = (args: AccessArgs<User>) => Promise<
  | boolean
  | {
      'user.id': {
        equals: string | undefined
      }
    }
>

export const adminOrCreateByWithValidSession: isAdminOrCreateByWithValidSession = async (args) => {
  const { req } = args
  const { user } = req
  //Check if user has the 'admin' role
  if (user && checkRole(['admin'], user)) {
    return true
  }

  const valid = await authenticatedWithValidSession({ req })
  if (valid) {
    return {
      'user.id': {
        equals: user?.id,
      },
    }
  } else {
    return false
  }
}
