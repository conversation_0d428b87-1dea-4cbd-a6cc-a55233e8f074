'use server'
import { LocaleCode } from '@/enums/locale.enum'
import {
  resendVerifyEmailTemplate,
  resetPasswordEmailTemplate,
  userSubscriptionNotificationEmailTemplate,
} from '@/utilities/getEmailTemplate'
import config from '@payload-config'
import { User } from '@sentry/nextjs'
import { getPayload } from 'payload'
export const sendEmailUserSubscriptionNotification = async (email: string, lang?: LocaleCode) => {
  const payload = await getPayload({ config })
  const template = userSubscriptionNotificationEmailTemplate({ email, lang })

  await payload.sendEmail(template)
}

export const sendEmailResetPasswordAction = async (email: string, lang?: LocaleCode) => {
  const payload = await getPayload({ config })

  const existUser = await payload.find({
    collection: 'users',
    where: { email: { equals: email } },
    pagination: false,
    limit: 1,
  })
  // Always return success to prevent revealing whether the email exists in the system

  if (existUser.docs.length === 0) {
    return
  }

  const token = await payload.forgotPassword({
    collection: 'users',
    data: {
      email: email,
    },
    disableEmail: true,
  })

  const template = resetPasswordEmailTemplate({ email, token, lang })

  await payload.sendEmail(template)
}

export const resendVerifyEmail = async (email: string, lang?: LocaleCode) => {
  const payload = await getPayload({ config })

  const { docs } = await payload.find({
    collection: 'users',
    limit: 1,
    showHiddenFields: true,
    where: { email: { equals: email } },
    select: { _verificationToken: true, name: true, _verified: true, email: true },
  })

  if (!docs?.length) {
    return { success: false, message: 'MES-306' }
  }

  const { _verificationToken, _verified } = docs[0] as User

  if (!_verificationToken || _verified) {
    return { success: false, message: 'MES-307' }
  }

  await payload.sendEmail(
    resendVerifyEmailTemplate({ user: docs[0], token: _verificationToken, lang }),
  )
  return { success: true }
}
