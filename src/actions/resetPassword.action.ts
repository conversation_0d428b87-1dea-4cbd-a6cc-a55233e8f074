'use server'
import { getPayload } from 'payload'
import config from '@payload-config'

export const resetPasswordAction = async ({
  password,
  token,
}: {
  password: string
  token: string
}) => {
  const payload = await getPayload({ config })

  const result = await payload.resetPassword({
    collection: 'users',
    data: {
      password: password,
      token: token,
    },
    overrideAccess: true,
  })
  return result
}
