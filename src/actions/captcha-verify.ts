'use server'

import { SCORE_THRESHOLD } from '@/constants/global.constant'
import { RecaptchaV3VerifyResponse } from '@/types/common.type'

const RECAPTCHA_VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify' as const

export interface VerifyResponse extends RecaptchaV3VerifyResponse {
  error?: string
}

export async function verifyCaptchaTokenAction(token: string): Promise<VerifyResponse> {
  try {
    // Environment validation
    const secretKey = process.env.CAPTCHA_SECRET_KEY
    if (!secretKey) {
      throw new Error('CAPTCHA_SECRET_KEY environment variable is not configured')
    }

    // Input validation
    if (!token || typeof token !== 'string') {
      return {
        success: false,
        score: 0,
        error: 'Invalid token provided',
        action: '',
        challenge_ts: new Date().toISOString(),
        hostname: '',
      }
    }

    // Prepare verification URL
    const verifyUrl = new URL(RECAPTCHA_VERIFY_URL)
    verifyUrl.searchParams.append('secret', secretKey)
    verifyUrl.searchParams.append('response', token)

    // Make verification request
    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },

      cache: 'no-store',
      next: { revalidate: 0 },
      signal: AbortSignal.timeout(5000),
    })

    if (!response.ok) {
      throw new Error(`reCAPTCHA verification failed with status: ${response.status}`)
    }

    const result: RecaptchaV3VerifyResponse = await response.json()

    return {
      ...result,
      // Override success if score is too low
      success: result.success && result.score >= SCORE_THRESHOLD,
    }
  } catch (error) {
    console.error('reCAPTCHA verification error:', error)

    return {
      success: false,
      score: 0,
      error: error instanceof Error ? error.message : 'Verification failed',
      action: '',
      challenge_ts: new Date().toISOString(),
      hostname: '',
    }
  }
}
