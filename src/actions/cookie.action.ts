'use server'

import { cookies } from 'next/headers'

interface CookieOptions {
  expires?: number
  path?: string
  secure?: boolean
  httpOnly?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  maxAge?: string
}

/**
 * Set a cookie with the specified name, value, and options
 * @param name Cookie name
 * @param value Cookie value
 * @param options Cookie options (expires in days, path, secure, httpOnly, sameSite)
 */
export const setCookie = async (name: string, value: string, options: CookieOptions = {}) => {
  const cookieStore = await cookies()

  const cookieOptions = {
    name: name,
    value,
    path: options.path ?? '/',
    secure: options.secure,
    httpOnly: options.httpOnly,
    sameSite: options.sameSite,
  }
  if (options?.maxAge) {
    cookieOptions['Max-Age'] = options?.maxAge
  }

  cookieStore.set(cookieOptions)
}

/**
 * Get a cookie value by name
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
export const getCookie = async (name: string) => {
  const cookieStore = await cookies()
  const cookie = cookieStore.get(name)

  return {
    value: cookie?.value ?? null,
    exists: !!cookie,
  }
}

/**
 * Delete a cookie by name
 * @param name <PERSON>ie name
 */
export const deleteCookie = async (name: string) => {
  const cookieStore = await cookies()
  cookieStore.delete(name)
}
