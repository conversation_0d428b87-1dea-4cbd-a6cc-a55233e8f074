'use server'

import { LOGIN_SESSION_COOKIE_KEY } from '@/constants/global.constant'
import config from '@payload-config'
import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'

/**
 * Gets the session token from the cookie and decodes it
 * @returns Object containing the session ID, token, and whether it exists
 */
export async function getLoginSessionIdFromCookie() {
  const cookieStore = await cookies()
  const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)

  if (!sessionToken?.value) {
    return {
      sessionId: null,
      sessionToken: null,
      exists: false,
    }
  }

  try {
    // Get payload instance
    const payload = await getPayload({ config })

    // Verify and decode the JWT token
    const decodedToken = jwt.verify(sessionToken.value, payload.secret) as {
      sessionId: string
      exp: number
    }

    // Check if token has expired
    if (decodedToken.exp < Math.floor(Date.now() / 1000)) {
      return {
        sessionId: null,
        sessionToken: sessionToken.value,
        exists: true,
        expired: true,
      }
    }

    return {
      sessionId: decodedToken.sessionId,
      sessionToken: sessionToken.value,
      exists: true,
      expired: false,
    }
  } catch (error) {
    console.error('Error decoding session token:', error)
    return {
      sessionId: null,
      sessionToken: sessionToken.value,
      exists: true,
      invalid: true,
    }
  }
}

/**
 * Validates if a session cookie exists and is valid
 * @returns Object containing validation result and session ID if valid
 */
export async function validateLoginSessionCookie() {
  const cookieStore = await cookies()
  const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value

  if (!sessionToken) {
    return { valid: false, message: 'No session cookie found' }
  }

  try {
    // Get payload instance
    const payload = await getPayload({ config })

    // Verify and decode the JWT token
    const decodedToken = jwt.verify(sessionToken, payload.secret) as {
      sessionId: string
      exp: number
    }

    // Check if token has expired
    if (decodedToken.exp < Math.floor(Date.now() / 1000)) {
      return { valid: false, message: 'Session token expired', expired: true }
    }

    return {
      valid: true,
      sessionId: decodedToken.sessionId,
      sessionToken,
    }
  } catch (error) {
    console.error('Error validating session token:', error)
    return { valid: false, message: 'Invalid session token', error: String(error) }
  }
}

/*  Gets the session ID from the cookie
 * @returns Object containing validation result and session ID if valid
 */
// export async function validateLoginSessionCookie() {
//  const cookieStore = await cookies()
//  const sessionId = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value

//  if (!sessionId) {
//    return { valid: false, message: 'No session cookie found' }
//  }

//  return {
//    valid: true,
//    sessionId,
//  }
// }

/**
 * Extends the login session cookie expiration
 * @param options Configuration options for extending the cookie
 * @returns Object indicating success or failure of the operation
 */
export async function extendLoginSessionCookie(options?: {
  maxAgeInSeconds?: number
  tokenExp?: number
}) {
  try {
    const { sessionToken, exists, expired, invalid } = await getLoginSessionIdFromCookie()

    // If cookie doesn't exist or is invalid, we can't extend it
    if (!exists || invalid) {
      return { success: false, message: 'No valid session cookie to extend' }
    }

    // Even if expired, we'll try to extend it if we have the token
    if (sessionToken) {
      const cookieStore = await cookies()

      // Calculate maxAge based on token expiration or use default
      let maxAge = options?.maxAgeInSeconds || 60 * 60 * 24 * 7 // Default to 7 days

      // If token expiration is provided, use it to calculate maxAge
      if (options?.tokenExp) {
        const currentTimeInSeconds = Math.floor(Date.now() / 1000)
        const timeUntilExpiration = options.tokenExp - currentTimeInSeconds

        // Only use token expiration if it's valid (positive and not too far in the future)
        if (timeUntilExpiration > 0) {
          maxAge = timeUntilExpiration
        }
      }

      // Set the cookie with the same value but new expiration
      cookieStore.set(LOGIN_SESSION_COOKIE_KEY, sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: maxAge,
      })

      return {
        success: true,
        message: 'Session cookie extended',
        wasExpired: expired || false,
        newMaxAge: maxAge,
      }
    }

    return { success: false, message: 'Failed to extend session cookie' }
  } catch (error) {
    console.error('Error extending session cookie:', error)
    return { success: false, message: 'Error extending session cookie', error: String(error) }
  }
}
