import { SCORE_THRESHOLD } from '@/constants/global.constant'
import { RecaptchaV3VerifyResponse } from '@/types/common.type'
import { NextRequest, NextResponse } from 'next/server'

const RECAPTCHA_VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify' as const

export async function POST(request: NextRequest) {
  try {
    // Input validation
    if (!process.env.CAPTCHA_SECRET_KEY) {
      return NextResponse.json(
        {
          success: false,
          score: 0,
          error: 'CAPTCHA_SECRET_KEY environment variable is not configured',
          action: '',
          challenge_ts: new Date().toISOString(),
          hostname: '',
        },
        { status: 500 },
      )
    }

    const { token } = await request.json()

    if (!token || typeof token !== 'string') {
      return NextResponse.json(
        {
          success: false,
          score: 0,
          error: 'Invalid token provided',
          action: '',
          challenge_ts: new Date().toISOString(),
          hostname: '',
        },
        { status: 400 },
      )
    }

    // Prepare verification URL
    const verifyUrl = new URL(RECAPTCHA_VERIFY_URL)
    verifyUrl.searchParams.append('secret', process.env.CAPTCHA_SECRET_KEY)
    verifyUrl.searchParams.append('response', token)

    // Verify with Google
    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      cache: 'no-store',
      next: { revalidate: 0 },
      signal: AbortSignal.timeout(5000),
    })

    if (!response.ok) {
      throw new Error(`reCAPTCHA verification failed with status: ${response.status}`)
    }

    const result: RecaptchaV3VerifyResponse = await response.json()

    // Return the full response with success flag adjusted based on score
    return NextResponse.json({
      ...result,
      success: result.success && result.score >= SCORE_THRESHOLD,
    })
  } catch (error) {
    console.error('reCAPTCHA verification error:', error)

    return NextResponse.json(
      {
        success: false,
        score: 0,
        error: error instanceof Error ? error.message : 'Verification failed',
        action: '',
        challenge_ts: new Date().toISOString(),
        hostname: '',
      },
      { status: 500 },
    )
  }
}
