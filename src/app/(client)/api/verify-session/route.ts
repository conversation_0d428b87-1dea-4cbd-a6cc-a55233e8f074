import { LOGIN_SESSION_COOKIE_KEY, PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { loginSessionService } from '@/services/login-session.service'
import config from '@payload-config'
import dayjs from 'dayjs'
import jwt from 'jsonwebtoken'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

// Set cache control headers to cache for 1 minute
// export const revalidate = 60

export async function GET(request: NextRequest) {
  const token = request.cookies.get(PORTAL_TOKEN_KEY)?.value
  const sessionToken = request.cookies.get(LOGIN_SESSION_COOKIE_KEY)?.value
  const payload = await getPayload({ config })
  if (!token) {
    return NextResponse.json({ valid: false, reason: 'no-token' }, { status: 401 })
  }

  const tokenValidation = validateToken(token, payload)
  if (!tokenValidation.valid || tokenValidation.expired) {
    return NextResponse.json(
      { valid: false, reason: tokenValidation.expired ? 'token-expired' : 'token-invalid' },
      { status: 401 },
    )
  }

  // Check if user has admin role, if so, skip session validation
  if (
    tokenValidation.payload &&
    tokenValidation.payload.roles &&
    Array.isArray(tokenValidation.payload.roles) &&
    tokenValidation.payload.roles.includes('admin')
  ) {
    return NextResponse.json({ valid: true })
  }

  if (!sessionToken) {
    return NextResponse.json({ valid: false, reason: 'no-session' }, { status: 401 })
  }

  try {
    // Verify and decode the JWT session token
    let sessionId: string
    try {
      // Get payload instance

      const decodedToken = jwt.verify(sessionToken, payload.secret) as {
        sessionId: string
        exp: number
      }

      // Check if token has expired
      if (decodedToken.exp < Math.floor(Date.now() / 1000)) {
        return NextResponse.json({ valid: false, reason: 'session-token-expired' }, { status: 401 })
      }

      sessionId = decodedToken.sessionId
    } catch (jwtError) {
      console.error('Session token validation failed:', jwtError)
      return NextResponse.json({ valid: false, reason: 'invalid-session-token' }, { status: 401 })
    }

    const res = await loginSessionService.getCurrentActiveLoginSession({
      params: { select: { id: true, expiredAt: true } },
      options: {
        headers: { Cookie: `${PORTAL_TOKEN_KEY}=${token}` } as HeadersInit,
      },
    })

    if (!res || res.id !== sessionId || !dayjs(res.expiredAt).isAfter(dayjs())) {
      return NextResponse.json({ valid: false, reason: 'session-expired' }, { status: 401 })
    }

    return NextResponse.json({ valid: true })
  } catch (error) {
    console.error('Session validation failed:', error)
    return NextResponse.json(
      { valid: false, reason: 'validation-error', error: String(error) },
      { status: 500 },
    )
  }
}

function validateToken(
  token: string,
  payload: any,
): { valid: boolean; expired: boolean; payload?: any } {
  try {
    const decoded = jwt.verify(token, payload.secret) as {
      sessionId: string
      exp: number
    }

    if (!decoded || typeof decoded !== 'object') {
      return { valid: false, expired: false }
    }

    if (decoded.exp && typeof decoded.exp === 'number') {
      const expirationTime = dayjs.unix(decoded.exp)
      const now = dayjs()

      if (now.isAfter(expirationTime)) {
        return { valid: true, expired: true, payload: decoded }
      }
    }

    return { valid: true, expired: false, payload: decoded }
  } catch (error) {
    console.error('Token validation error:', error)
    return { valid: false, expired: false }
  }
}
