import { LocaleEnum } from '@/enums/locale.enum'
import config from '@payload-config'
import { getServerSideSitemap } from 'next-sitemap'
import { unstable_cache } from 'next/cache'
import { getPayload } from 'payload'

const getMedicinesSitemap = unstable_cache(
  async () => {
    const payload = await getPayload({ config })
    const SITE_URL =
      process.env.NEXT_PUBLIC_SERVER_URL ||
      process.env.VERCEL_PROJECT_PRODUCTION_URL ||
      'https://example.com'

    const results = await payload.find({
      collection: 'medicines',
      overrideAccess: false,
      draft: false,
      depth: 0,
      limit: 1000,
      pagination: false,
      where: {
        _status: {
          equals: 'published',
        },
      },
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const dateFallback = new Date().toISOString()

    const sitemap = results.docs
      ? results.docs
          .filter((medicicine) => Boolean(medicicine?.slug))
          .map((medicicine) => ({
            loc: `${SITE_URL}/medicine-and-supplements/${medicicine?.slug}`,
            lastmod: medicicine.updatedAt || dateFallback,
            priority: 0.8,
            alternateRefs: Object.values(LocaleEnum).map((locale) => ({
              href: `${SITE_URL}/${locale}/medicine-and-supplements/${medicicine?.slug}`,
              hreflang: locale,
            })),
          }))
      : []

    return sitemap
  },
  ['medicines-sitemap'],
  {
    tags: ['medicines-sitemap'],
  },
)

export async function GET() {
  const sitemap = await getMedicinesSitemap()

  return getServerSideSitemap(sitemap)
}
