import React from 'react'

import '@/styles/global.scss'
import 'photoswipe/dist/photoswipe.css'
import 'swiper/css'
import 'swiper/css/effect-coverflow'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html suppressHydrationWarning>
      <head>
        <link href="/favicon-wap.png" rel="icon" sizes="50x50" />

        {/* ✅ Prevent zoom on mobile / WebView */}
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </head>

      <body suppressHydrationWarning>{children}</body>
    </html>
  )
}
