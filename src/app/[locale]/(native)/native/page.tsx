import { NativePage } from '@/components/NativePage/NativePage'
import { MAX_AGE_TOKEN, X_ACCESS_TOKEN, X_LOGIN_SESSION } from '@/constants/storageKeys.constant'

import { headers } from 'next/headers'

type SearchParams = Promise<{ [key: string]: string | undefined }>
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams
  const { redirectUrl, lang, showFurigana, isNativeEnvironment } = paramsValue
  const headersList = await headers()
  const accessToken = headersList.get(X_ACCESS_TOKEN) || undefined
  const loginSession = headersList.get(X_LOGIN_SESSION) || undefined
  const maxAgeToken = headersList.get(MAX_AGE_TOKEN) || undefined

  return (
    <NativePage
      accessToken={accessToken}
      loginSession={loginSession}
      maxAgeToken={maxAgeToken}
      redirectUrl={redirectUrl}
      lang={lang}
      showFurigana={showFurigana}
      isNativeEnvironment={isNativeEnvironment}
    />
  )
}
