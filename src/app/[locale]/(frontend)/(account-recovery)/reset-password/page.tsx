'use client'
import React, { useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { Button } from '@/components/ui/Button/Button'
import Link from 'next/link'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { resetPasswordAction } from '@/actions/resetPassword.action'
import { useToast } from '@/hooks/common/useToast'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { cn } from '@/utilities/cn'
import close from '@/assets/icons/left.svg'
import logo from '@/assets/images/logo.png'
import banner from '@/assets/icons/Group-reset.svg'
import Image from 'next/image'
import { InputPassword } from '@/components/ui/Input/InputPassword'
import { useVerifyCaptcha } from '@/hooks/common/useVerifyCaptcha'

const Page = () => {
  const { verifyCaptcha, isVerifying } = useVerifyCaptcha()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isResetPasswordSuccess, setIsResetPasswordSuccess] = useState(false)
  const { getAllSearchQueries } = useSearchQuery()
  const { token } = getAllSearchQueries()
  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations()
  const formSchema = z
    .object({
      password: z.string().min(8, {
        message: t('MES-178'),
      }),
      confirmPassword: z.string().min(1, {
        message: t('MES-179'),
      }),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('MES-117'),
      path: ['confirmPassword'],
    })
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    mode: 'onTouched',
  })
  const {
    formState: { errors },
  } = form
  const onResetPassword = async (values: z.infer<typeof formSchema>) => {
    if (!token || !values.password) return
    const result = await verifyCaptcha({
      action: 'send_email_reset_password',
      showToastError: true,
    })
    if (result?.success) {
      try {
        setIsSubmitting(true)
        await resetPasswordAction({
          password: values.password,
          token,
        })
        setIsSubmitting(false)
        toast({
          title: t('MES-180'),
          variant: 'success',
          description: t('MES-181'),
        })
        setIsResetPasswordSuccess(true)
        setTimeout(() => {
          router.replace('/')
        }, 3000)
      } catch (error) {
        console.error(error)
        setIsSubmitting(false)
        toast({
          title: t('MES-184'),
          variant: 'error',
          description: t('MES-182'),
        })
        setIsResetPasswordSuccess(false)
      }
    }
  }

  return (
    <div className="mobile-wrapper flex h-[100dvh] flex-col">
      <Link href={'/auth/login'} className="h-10 p-3">
        <Image src={close} alt="close" width={24} height={24} className="size-6"></Image>
      </Link>
      {!token ? (
        <div className="flex flex-1 flex-col items-center justify-center p-4">
          <div className="w-80 rounded-lg bg-white p-6 shadow-md">
            <h2 className="mb-4 text-center text-xl font-semibold">{t('MES-209')}</h2>
            <div className="rounded-md bg-red-100 p-4 text-red-700">
              <p className="text-center">{t('MES-210')}</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-1 flex-col items-center justify-center p-4">
          <Image
            src={logo}
            alt="logo"
            width={200}
            height={50}
            className="mb-8 h-[50px] w-[200px] object-contain"
          ></Image>

          {isResetPasswordSuccess && <Image src={banner} alt="banner " className="mb-8"></Image>}

          <div className="w-full">
            <h2 className="typo-heading-7 mb-2 text-center">
              {isResetPasswordSuccess ? t('MES-207') : t('MES-176')}
            </h2>

            <p className="typo-field-2 text-center">
              {isResetPasswordSuccess ? t('MES-208') : t('MES-178')}
            </p>
            {!isResetPasswordSuccess && (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onResetPassword)} className="my-6 space-y-3">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <InputPassword
                            wrapperClassname="h-[52px] px-4"
                            placeholder={t('MES-118')}
                            isError={Boolean(errors?.[field.name])}
                            {...field}
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <InputPassword
                            wrapperClassname="h-[52px] px-4"
                            placeholder={t('MES-116')}
                            isError={Boolean(errors?.[field.name])}
                            {...field}
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    disabled={isSubmitting || isVerifying}
                    className={cn(
                      `text-whitetransition-colors !mt-6 w-full rounded-md py-3 font-semibold text-white`,
                      !form.formState.isValid && '!bg-custom-neutral-100 !text-custom-neutral-300',
                      isSubmitting || isVerifying ? 'opacity-75' : 'bg-primary hover:bg-blue-700',
                    )}
                  >
                    {isSubmitting || isVerifying ? (
                      <Spinner className="size-6 border-blue-500 border-t-white"></Spinner>
                    ) : (
                      t('MES-169')
                    )}
                  </Button>
                </form>
              </Form>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default Page
