'use client'
import { sendEmailResetPasswordAction } from '@/actions/email.action'
import arrow from '@/assets/icons/arrow-left-primary.svg'
import google from '@/assets/icons/google.svg'
import logo from '@/assets/images/logo.png'
import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { Input } from '@/components/ui/Input/Input'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { useToast } from '@/hooks/common/useToast'
import { useVerifyCaptcha } from '@/hooks/common/useVerifyCaptcha'
import { cn } from '@/utilities/cn'
import { zodResolver } from '@hookform/resolvers/zod'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
const Page = () => {
  const t = useTranslations()
  const locale = useLocale()
  const { verifyCaptcha, isVerifying } = useVerifyCaptcha()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSendEmailSuccess, setIsSendEmailSuccess] = useState(false)
  const { toast } = useToast()
  const formSchema = z.object({
    email: z.string().email({
      message: t('MES-225'),
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
    mode: 'onTouched',
  })
  const {
    formState: { errors },
  } = form
  const onSendEmailResetPassword = async (values: z.infer<typeof formSchema>) => {
    if (!values.email) return
    const result = await verifyCaptcha({
      action: 'send_email_reset_password',
      showToastError: true,
    })
    if (result?.success) {
      try {
        setIsSubmitting(true)
        await sendEmailResetPasswordAction(values.email, locale)
        setIsSubmitting(false)
        setIsSendEmailSuccess(true)
        toast({
          title: t('MES-172'),
          variant: 'success',
          description: t('MES-173'),
        })
      } catch (error) {
        console.error(error)
        setIsSubmitting(false)
        toast({
          title: t('MES-174'),
          variant: 'error',
          description: t('MES-175'),
        })
      }
    }
  }

  return (
    <div className="mobile-wrapper flex h-[100dvh] flex-col">
      <Link href={'/auth/login'} className="h-10 p-3">
        <Image src={arrow} alt="close" width={24} height={24} className="size-6"></Image>
      </Link>
      <div className="flex flex-1 flex-col items-center justify-center p-4">
        <Image
          src={logo}
          alt="logo"
          width={200}
          height={50}
          className="mb-8 h-[50px] w-[200px] object-contain"
        ></Image>
        <div className="w-full">
          <h2 className="typo-heading-7 mb-2 text-center">
            {isSendEmailSuccess ? t('MES-205') : t('MES-216')}
          </h2>

          <p className="typo-field-2 text-center">
            {isSendEmailSuccess ? t('MES-206') : t('MES-204')}
          </p>
          {!isSendEmailSuccess ? (
            <>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSendEmailResetPassword)}
                  className="my-6 space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="Email"
                            className={cn('h-[52px] px-4')}
                            isError={Boolean(errors?.[field.name])}
                            {...field}
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    disabled={isSubmitting || isVerifying}
                    className={cn(
                      `w-full rounded-md py-3 font-semibold text-white transition-colors`,
                      !form.formState.isValid && '!bg-custom-neutral-100 !text-custom-neutral-300',
                      isSubmitting || isVerifying ? 'opacity-75' : 'bg-primary hover:bg-blue-700',
                    )}
                  >
                    {isSubmitting || isVerifying ? (
                      <Spinner className="size-6 border-blue-500 border-t-white"></Spinner>
                    ) : (
                      t('MES-169')
                    )}
                  </Button>
                </form>
              </Form>
              <div className="typo-field-2 mb-2 text-center text-subdued"> {t('MES-07')} </div>

              <a href={`${process.env.NEXT_PUBLIC_SERVER_URL}/api/oauth/authorization/google`}>
                <Image
                  src={google}
                  alt="google"
                  width={30}
                  height={30}
                  className="mx-auto mb-6 size-[30px]"
                ></Image>
              </a>

              <p className="typo-body-6 mt-6 text-center">
                {t('MES-08')}
                <Link href="/auth/signup" className="ml-1 text-primary hover:text-blue-700">
                  {t('MES-09')}
                </Link>
              </p>
            </>
          ) : (
            <p className="typo-body-6 mt-6 text-center">
              {t('MES-77')}
              <Link href="/auth/signup" className="ml-2 text-primary hover:text-blue-700">
                {t('MES-06')}
              </Link>
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

export default Page
