'use client'
import { resendVerifyEmail } from '@/actions/email.action'
import arrow from '@/assets/icons/arrow-left-primary.svg'
import logo from '@/assets/images/logo.png'
import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { Input } from '@/components/ui/Input/Input'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { RESEND_VERIFY_COOLDOWN_STORAGE_KEY } from '@/constants/storageKeys.constant'
import { useLoading } from '@/hooks/common/useLoading'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useToast } from '@/hooks/common/useToast'
import { useVerifyCaptcha } from '@/hooks/common/useVerifyCaptcha'
import { localStorageService } from '@/services/local-storage.service'
import { cn } from '@/utilities/cn'
import { zodResolver } from '@hookform/resolvers/zod'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const COOLDOWN_TIME = 300

// Helper function to format seconds to MM:SS
const formatTimeMMSS = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const Page = () => {
  const t = useTranslations()
  const locale = useLocale()
  const { showLoading, hideLoading } = useLoading()
  const { getAllSearchQueries, updateSearchQuery } = useSearchQuery()
  const { status, email: emailOnParams } = getAllSearchQueries()
  const { verifyCaptcha, isVerifying } = useVerifyCaptcha()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isResendEmailSuccess, setIsResendEmailSuccess] = useState(
    status === 'success' ? true : false,
  )
  const [cooldownTime, setCooldownTime] = useState<number>(0)

  const { toast } = useToast()
  const formSchema = z.object({
    email: z.string().email({
      message: t('MES-225'),
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: emailOnParams || '',
    },
    mode: 'onTouched',
  })
  const {
    formState: { errors },
  } = form

  // Load cooldown data from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const storedCooldownData = localStorageService.getLocalItem(
          RESEND_VERIFY_COOLDOWN_STORAGE_KEY,
        )
        if (storedCooldownData) {
          const { expiryTime, email } = JSON.parse(storedCooldownData)

          // Only apply cooldown if it's for the current email
          if (email === emailOnParams) {
            const currentTime = Math.floor(Date.now() / 1000)
            const remainingTime = Math.max(0, expiryTime - currentTime)

            if (remainingTime > 0) {
              setCooldownTime(remainingTime)
            } else {
              // Clear expired cooldown
              localStorageService.removeLocalItem(RESEND_VERIFY_COOLDOWN_STORAGE_KEY)
            }
          }
        }
      } catch (error) {
        console.error('Error loading cooldown from localStorage:', error)
        localStorageService.removeLocalItem(RESEND_VERIFY_COOLDOWN_STORAGE_KEY)
      }
    }
  }, [emailOnParams])

  const saveCooldownToLocalStorage = (email: string, seconds: number) => {
    if (typeof window !== 'undefined') {
      try {
        const currentTime = Math.floor(Date.now() / 1000)
        const expiryTime = currentTime + seconds

        localStorageService.setLocalItem(
          RESEND_VERIFY_COOLDOWN_STORAGE_KEY,
          JSON.stringify({
            email,
            expiryTime,
          }),
        )
      } catch (error) {
        console.error('Error saving cooldown to localStorage:', error)
      }
    }
  }

  const onResendVerifyEmail = async (email?: string) => {
    let emailToSend = email ? email : (emailOnParams ?? '')
    if (!emailToSend) return
    setIsSubmitting(true)

    const result = await verifyCaptcha({
      action: 'resend_verify_email',
      showToastError: true,
    })

    if (!result?.success) {
      setIsSubmitting(false)
      return
    }

    try {
      const response = await resendVerifyEmail(emailToSend, locale)
      const success = response?.success

      toast({
        title: t(success ? 'MES-305' : response?.message || 'MES-174'),
        variant: success ? 'success' : 'error',
      })
      setIsResendEmailSuccess(success)
      if (success) {
        updateSearchQuery({ status: 'success', email: emailToSend || '' }, 'replace')
        setCooldownTime(COOLDOWN_TIME)
        saveCooldownToLocalStorage(emailToSend, COOLDOWN_TIME)
      }
    } catch (error) {
      console.error(error)
      toast({ title: t('MES-174'), variant: 'error' })
      setIsResendEmailSuccess(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!values.email) return
    await onResendVerifyEmail(values.email)
  }

  useEffect(() => {
    if (cooldownTime > 0) {
      const timer = setInterval(() => {
        setCooldownTime((prev) => {
          const newValue = Math.max(0, prev - 1)
          // If cooldown reaches zero, remove from localStorage
          if (newValue === 0 && typeof window !== 'undefined') {
            localStorageService.removeLocalItem(RESEND_VERIFY_COOLDOWN_STORAGE_KEY)
          }
          return newValue
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [cooldownTime])

  return (
    <div className="mobile-wrapper flex h-[100dvh] flex-col">
      <Link href={'/'} className="h-10 p-3">
        <Image src={arrow} alt="close" width={24} height={24} className="size-6"></Image>
      </Link>
      <div className="flex flex-1 flex-col items-center justify-center p-4">
        <Image
          src={logo}
          alt="logo"
          width={200}
          height={50}
          className="mb-8 h-[50px] w-[200px] object-contain"
        ></Image>
        <div className="w-full">
          <h2 className="typo-heading-7 mb-2 text-center">
            {isResendEmailSuccess ? t('MES-205') : t('MES-296')}
          </h2>

          <p className="typo-field-2 text-center">
            {isResendEmailSuccess ? t('MES-300') : t('MES-297')}
          </p>
          {!isResendEmailSuccess ? (
            <>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(formSubmit)} className="my-6 space-y-6">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="Email"
                            className={cn('h-[52px] px-4')}
                            isError={Boolean(errors?.[field.name])}
                            {...field}
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    disabled={isSubmitting || isVerifying}
                    className={cn(
                      `w-full rounded-md py-3 font-semibold text-white transition-colors`,
                      !form.formState.isValid && '!bg-custom-neutral-100 !text-custom-neutral-300',
                      isSubmitting || isVerifying ? 'opacity-75' : 'bg-primary hover:bg-blue-700',
                    )}
                  >
                    {isSubmitting || isVerifying ? (
                      <Spinner className="size-6 border-blue-500 border-t-white"></Spinner>
                    ) : (
                      t('MES-298')
                    )}
                  </Button>
                </form>
              </Form>
            </>
          ) : (
            <p className="typo-body-6 mt-6 text-center">
              {t('MES-302')}

              {/* resend button */}
              <button
                onClick={async () => {
                  try {
                    showLoading()
                    await onResendVerifyEmail()
                  } finally {
                    hideLoading()
                  }
                }}
                disabled={cooldownTime > 0 || isSubmitting}
                className="ml-2 text-primary hover:text-blue-700"
              >
                {cooldownTime > 0 ? (
                  <span className="text-subdued">
                    {t('MES-308')}
                    &nbsp;
                    <span className="inline-block w-10 text-danger-600">
                      {' '}
                      {formatTimeMMSS(cooldownTime)}
                    </span>
                  </span>
                ) : (
                  t('MES-303')
                )}
              </button>
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

export default Page
