'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button/Button'
import { LoadingPage } from '@/components/ui/Loading/LoadingPage'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { verifyUserEmailAction } from '@/actions/verifyUserEmail.action'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import close from '@/assets/icons/left.svg'
import Image from 'next/image'
import logo from '@/assets/images/logo.png'
import banner from '@/assets/icons/Group-reset.svg'
export default function Page() {
  const [loading, setLoading] = useState(true)
  const [success, setSuccess] = useState(false)
  const { getAllSearchQueries } = useSearchQuery()
  const { token } = getAllSearchQueries()
  const router = useRouter()
  const t = useTranslations()
  const verifyEmail = async (token: string) => {
    try {
      setLoading(true)

      await verifyUserEmailAction(token)

      setSuccess(true)
    } catch (error) {
      console.log(error)
      setSuccess(false)
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    if (token) verifyEmail(token)
  }, [token])
  if (loading) return <LoadingPage />
  return (
    <div className="mobile-wrapper flex h-[100dvh] flex-col">
      <Link href={'/'} className="h-10 p-3">
        <Image src={close} alt="close" width={24} height={24} className="size-6"></Image>
      </Link>
      <div className="flex flex-1 flex-col items-center justify-center p-4">
        {
          <Image
            src={logo}
            alt="logo"
            width={200}
            height={50}
            className="mb-8 h-[50px] w-[200px] object-contain"
          ></Image>
        }

        {success && <Image src={banner} alt="banner " className="mb-8"></Image>}

        {success && (
          <div className="space-y-1 text-center">
            <h2 className="text-2xl font-semibold">{t('MES-211')}</h2>
            <p>{t('MES-212')}</p>
            <Button onClick={() => router.push('/auth/login')} className="!mt-6">
              {' '}
              {t('MES-06')}{' '}
            </Button>
          </div>
        )}

        {!success && (
          <div className="space-y-1 text-center">
            <h2 className="text-2xl font-semibold text-red-500">{t('MES-213')}</h2>
            <p>{t('MES-214')}</p>
            <Button onClick={() => router.push('/auth/signup')} className="!mt-6">
              {t('MES-09')}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
