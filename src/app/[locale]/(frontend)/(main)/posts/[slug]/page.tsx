import { LivePreviewListener } from '@/components/LivePreviewListener'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import RichText from '@/components/RichText'
import { ShareLinkButton } from '@/components/ShareLinkButton/ShareLinkButton'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'

import { PostCardItem } from '@/features/post/components/PostCardItem/PostCardItem'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import type { Media, Post } from '@/payload-types'
import { postService } from '@/services/post.service'
import { cn } from '@/utilities/cn'
import { dateToYMD } from '@/utilities/date'
import { generateMetaPost } from '@/utilities/generateMeta'
import type { Metadata } from 'next'
import { getLocale, getTranslations } from 'next-intl/server'
import { cookies, draftMode } from 'next/headers'
import Image from 'next/image'
import { cache } from 'react'

type Args = {
  params: Promise<{
    slug?: string
    locale: string
  }>
}

export default async function Post({ params: paramsPromise }: Args) {
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  // const payload = await getPayload({ config })
  const locale = await getLocale()
  // const headers = await nextHeaders()
  // const { user } = await payload.auth({ headers })
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const url = '/posts/' + slug
  const post = await queryPostBySlug({ slug, locale })
  const { title, heroImage, content, createdAt, relatedPosts } = post || {}
  const { sizes } = (heroImage as Media) || {}
  const t = await getTranslations()
  const isShowAdsense = true
  if (!post) return <PayloadRedirects url={url} />
  const relatedPostsFilter: Post[] = Array.isArray(relatedPosts)
    ? relatedPosts.filter((post): post is Post => typeof post === 'object')
    : []
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <div className="flex items-center justify-between px-4 pt-4">
        <PreviousPageButton fallbackURL="/posts" isNative={isNative}></PreviousPageButton>
        <ShareLinkButton title={title} text={title} className="ml-auto"></ShareLinkButton>
      </div>
      <article className="flex flex-col gap-y-4 p-4">
        {/* Image */}
        {sizes?.thumbnail?.url && (
          <div className={cn('relative h-[177px] w-full overflow-hidden rounded-lg')}>
            <Image
              src={sizes?.thumbnail?.url}
              alt={title || ''}
              fill
              className="h-full w-full object-cover"
              sizes="400px"
            ></Image>
          </div>
        )}
        {createdAt && <time className="typo-body-7">{dateToYMD(new Date(createdAt))}</time>}

        <h1 className="typo-heading-7">{title}</h1>

        <div className="space-y-4">
          {/* Content */}
          {content && (
            <RichText
              className="mx-auto max-w-[48rem]"
              data={content}
              enableGutter={false}
              showADS={isShowAdsense}
            />
          )}

          {/* Related */}
          {relatedPostsFilter && relatedPostsFilter.length > 0 && (
            <div className="space-y-3">
              <h4 className="typo-body-3">{t('MES-78')}</h4>
              <div className="space-y-3">
                {relatedPostsFilter?.map((post: Post) => (
                  <PostCardItem key={post.id} post={post} variant="row"></PostCardItem>
                ))}
              </div>
            </div>
          )}
        </div>
      </article>
      <PayloadRedirects disableNotFound url={url} />
      {draft && <LivePreviewListener />}
    </MainPageWrapper>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const locale = await getLocale()
  const post = await queryPostBySlug({ slug, locale })
  if (!post) return {}
  return generateMetaPost(post)
}

const queryPostBySlug = cache(async ({ slug, locale }: { slug: string; locale: string }) => {
  const result = await postService.getPosts({
    params: {
      // draft: false,
      limit: 1,
      depth: 2,
      pagination: false,
      where: {
        slug: {
          equals: slug,
        },
        language: {
          equals: locale,
        },
      },
    },
    options: {
      cache: 'no-store',
      next: {},
    },
  })

  return result?.docs?.[0] || null
})
