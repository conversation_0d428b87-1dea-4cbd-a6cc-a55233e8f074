import React from 'react'
import { PostsContainer } from '@/features/post/containers/PostsContainer/PostsContainer'
import type { Metadata } from 'next'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import { LivestreamPopup } from '@/components/Popup/LivestreamPopup/LivestreamPopup'
import { POSTS_PAGE_CONTAINER_WRAPPER_ID } from '@/constants/global.constant'

// Type
type Params = Promise<{ slug: string; locale: string }>
type SearchParams = Promise<{ [key: string]: string | undefined }>
// Meta Data

export async function generateMetadata({}: { params: Params }): Promise<Metadata> {
  const t = await getTranslations()

  return {
    title: t('MES-19'),
  }
}

// Page
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams
  return (
    <>
      <MainPageWrapper withSubheader className="bg-white" id={POSTS_PAGE_CONTAINER_WRAPPER_ID}>
        <PostsContainer params={paramsValue}></PostsContainer>
        <LivestreamPopup boundsElementId={POSTS_PAGE_CONTAINER_WRAPPER_ID}></LivestreamPopup>
      </MainPageWrapper>
    </>
  )
}
