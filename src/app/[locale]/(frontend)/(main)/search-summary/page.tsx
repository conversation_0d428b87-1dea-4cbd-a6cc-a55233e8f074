import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { SearchSummaryContainer } from '@/features/search-summary/containers/SearchSummaryContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

type SearchParams = Promise<{ [key: string]: string | undefined }>
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-66'),
  }
}
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  const paramsValue = await searchParams
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <SearchSummaryContainer
        searchParams={paramsValue}
        isNative={isNative}
      ></SearchSummaryContainer>
    </MainPageWrapper>
  )
}
