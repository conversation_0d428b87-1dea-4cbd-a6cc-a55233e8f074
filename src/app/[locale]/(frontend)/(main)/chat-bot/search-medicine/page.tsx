import { ChatWindowWrapper } from '@/components/ChatBot/SearchMedicineChat/ChatWindow/ChatWindowWrapper'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-589'),
  }
}

export default async function SearchMedicinePage() {
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  return (
    <MainPageWrapper withSubheader={false}>
      <ChatWindowWrapper isNative={isNative} />
    </MainPageWrapper>
  )
}
