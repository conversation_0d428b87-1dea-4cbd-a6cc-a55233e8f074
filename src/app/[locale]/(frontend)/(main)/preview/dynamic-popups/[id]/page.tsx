import { CustomDynamicPopup } from '@/components/CustomDynamicPopup/CustomDynamicPopup'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { <PERSON>alog, DialogContent, DialogTitle } from '@/components/ui/Dialog/Dialog'
import { LOGIN_SESSION_COOKIE_KEY, PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { dynamicPopupService } from '@/services/dynamic-popup.service'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { getLocale } from 'next-intl/server'
import { cookies, draftMode } from 'next/headers'
import { cache } from 'react'

type Args = {
  params: Promise<{
    id?: string
    locale: string
  }>
}

export default async function Page({ params: paramsPromise }: Args) {
  const locale = await getLocale()
  const { isEnabled: draft } = await draftMode()
  const { id = '' } = await paramsPromise
  const url = '/preview/dynamic-popups/' + id

  // Only use draft content when draft mode is actually enabled
  // This is critical to prevent infinite reloads
  const popup = await queryPopupById({
    id,
    locale,
    draft,
  })

  if (!popup) return <PayloadRedirects url={url} />

  // Only enable LivePreviewListener when draft mode is truly enabled by the system
  // Using searchParams to force it can lead to infinite reloads
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <div className="max-w-[336px]">
        <Dialog open={true}>
          <DialogContent className="mobile-wrapper !max-w-[336px] overflow-hidden border-none p-0">
            <VisuallyHidden>
              <DialogTitle>{popup.name}</DialogTitle>
            </VisuallyHidden>
            <CustomDynamicPopup data={popup} />
          </DialogContent>
        </Dialog>
      </div>
      {/* <PayloadRedirects disableNotFound url={url} /> */}
      {draft && <LivePreviewListener />}
    </MainPageWrapper>
  )
}

const queryPopupById = cache(
  async ({ id, locale, draft = false }: { id: string; locale: string; draft?: boolean }) => {
    const cookieStore = await cookies()
    const token = cookieStore.get(PORTAL_TOKEN_KEY)?.value
    const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value
    const result = await dynamicPopupService.getPopupById({
      id,
      params: {
        locale,
        // Only pass draft: true when we're in draft mode
        ...(draft ? { draft: true } : {}),
      },
      options: {
        next: {
          // revalidate: 60,
          // tags: ['posts'],
        },
        cache: 'no-store',
        headers: {
          Cookie: `${PORTAL_TOKEN_KEY}=${token}; ${LOGIN_SESSION_COOKIE_KEY}=${sessionToken}`,
        },
      },
    })

    return result || null
  },
)
