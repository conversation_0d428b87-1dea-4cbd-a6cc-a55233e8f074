import { HomeContainer } from '@/features/home/<USER>/Home/HomeContainer'
import { routing } from '@/i18n/routing'
import { MainFooter } from '@/layouts/MainLayout/MainFooter/MainFooter'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { setRequestLocale } from 'next-intl/server'

export default async function Page({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  setRequestLocale(locale)
  return (
    <MainPageWrapper withSubheader className="bg-white">
      <HomeContainer />
      <MainFooter></MainFooter>
    </MainPageWrapper>
  )
}
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}
