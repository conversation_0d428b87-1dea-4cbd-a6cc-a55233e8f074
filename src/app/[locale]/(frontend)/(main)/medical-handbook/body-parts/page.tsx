import { BodyPartsContainer } from '@/features/body-parts/containers/BodyPartsContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-36'),
  }
}
export default async function Page() {
  return (
    <MainPageWrapper withSubheader className="bg-custom-background-hover">
      <BodyPartsContainer />
    </MainPageWrapper>
  )
}
