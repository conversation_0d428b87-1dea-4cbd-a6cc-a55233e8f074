import React from 'react'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { MedicalFacultiesContainer } from '@/features/medical-faculty/containers/MedicalFacultiesContainer/MedicalFacultiesContainer'
import { getTranslations } from 'next-intl/server'

type Args = {
  searchParams: Promise<{ [key: string]: string | undefined }>
}
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-35'),
  }
}
export default async function Page({}: Args) {
  return (
    <MainPageWrapper withSubheader>
      <MedicalFacultiesContainer></MedicalFacultiesContainer>
    </MainPageWrapper>
  )
}
