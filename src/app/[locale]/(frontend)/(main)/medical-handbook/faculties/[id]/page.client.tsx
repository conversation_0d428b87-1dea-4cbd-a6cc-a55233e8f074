'use client'
import { IframeContainer } from '@/components/IframeContainer/IframeContainer'
import { SHOW_FURIGANA } from '@/constants/storageKeys.constant'
import { localStorageService } from '@/services/local-storage.service'
import { useEffect, useState } from 'react'

export default function PageClient({
  id,
  searchParams,
}: {
  id: string
  searchParams?: { [key: string]: string | undefined }
}) {
  const { tutorial } = searchParams || {}

  const [isShowFurigana, setIsShowFurigana] = useState(false)
  const portalURL = tutorial
    ? `${process.env.NEXT_PUBLIC_PORTAL_URL}/mobile/medical-notebook?id=${id}&showFurigana=${isShowFurigana}&isDemo=true`
    : `${process.env.NEXT_PUBLIC_PORTAL_URL}/mobile/medical-notebook?id=${id}&showFurigana=${isShowFurigana}`
  useEffect(() => {
    const showFurigana = localStorageService.getLocalItem(SHOW_FURIGANA)
    setIsShowFurigana(showFurigana)
  }, [])
  useEffect(() => {
    const header = document.getElementById('wap-main-header')

    if (!header) return

    let overlay = document.getElementById('tutorial-header-overlay')

    if (tutorial && tutorial === 'true') {
      if (!overlay) {
        overlay = document.createElement('div')
        overlay.id = 'tutorial-header-overlay'
        overlay.classList.add(
          'absolute',
          'top-0',
          'left-0',
          'w-full',
          'h-full',
          'bg-[#00000066]',
          'pointer-events-none',
          'z-[9999]',
        )
        header.classList.add('pointer-events-none')
        header.appendChild(overlay)
      }
    } else {
      overlay?.remove()
      header.classList.remove('pointer-events-none')
    }

    return () => {
      overlay?.remove()
      header.classList.remove('pointer-events-none')
    }
  }, [tutorial])

  return <IframeContainer src={portalURL}></IframeContainer>
}
