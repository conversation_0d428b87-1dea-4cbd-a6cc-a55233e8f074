import { PortalPageWrapper } from '@/components/PortalPageWrapper/PortalPageWrapper'
import PageClient from './page.client'

type Args = {
  params: Promise<{
    id: string
  }>
  searchParams: Promise<{ [key: string]: string | undefined }>
}
export default async function Page({ params: paramsPromise, searchParams }: Args) {
  const { id } = await paramsPromise
  const { tutorial } = (await searchParams) || {}
  return (
    <PortalPageWrapper>
      <PageClient id={id} searchParams={{ tutorial }}></PageClient>
    </PortalPageWrapper>
  )
}
