import { MedicalFacultiesSearchContainer } from '@/features/medical-faculty/containers/MedicalFacultiesSearchContainer/MedicalFacultiesSearchContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'

type SearchParams = Promise<{ [key: string]: string | undefined }>
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-125'),
  }
}
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <MedicalFacultiesSearchContainer searchParams={paramsValue}></MedicalFacultiesSearchContainer>
    </MainPageWrapper>
  )
}
