import { FavoriteMedicinesSearchContainer } from '@/features/user/containers/FavoriteMedicinesSearchContainer/FavoriteMedicinesSearchContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'

type SearchParams = Promise<{ [key: string]: string | undefined }>
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-54'),
  }
}
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams
  return (
    <MainPageWrapper className="bg-custom-background-hover" withSubheader={false}>
      <FavoriteMedicinesSearchContainer
        searchParams={paramsValue}
      ></FavoriteMedicinesSearchContainer>
    </MainPageWrapper>
  )
}
