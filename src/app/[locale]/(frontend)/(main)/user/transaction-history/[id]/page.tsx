import TransactionDetailsContainer from '@/features/user/containers/TransactionDetailsContainer/TransactionDetailsContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'

type Args = {
  params: Promise<{
    id: string
  }>
}

const Page = async ({ params: paramsPromise }: Args) => {
  const { id = '' } = await paramsPromise
  return (
    <MainPageWrapper withSubheader={false} className="flex flex-col bg-custom-background-hover">
      <TransactionDetailsContainer id={id}></TransactionDetailsContainer>
    </MainPageWrapper>
  )
}

export default Page
