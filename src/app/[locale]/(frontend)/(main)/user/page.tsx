import { PortalPageWrapper } from '@/components/PortalPageWrapper/PortalPageWrapper'
import { UserContainer } from '@/features/user/containers/UserContainer/UserContainer'
import React from 'react'
import { getTranslations } from 'next-intl/server'
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-198'),
  }
}
const Page = () => {
  return (
    <PortalPageWrapper>
      <UserContainer></UserContainer>
    </PortalPageWrapper>
  )
}

export default Page
