import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { ProductsSearch } from '@/features/product/components/ProductComponents/ProductList/ProductSearchContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

type SearchParams = Promise<{ [key: string]: string | undefined }>
export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('MES-84'),
  }
}
export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams
  const cookieStore = await cookies()
  const isNativeEnvironment = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      {/* <MedicinesSearchContainer searchParams={paramsValue}></MedicinesSearchContainer> */}
      <ProductsSearch
        searchParams={paramsValue}
        isNativeEnvironment={isNativeEnvironment}
      ></ProductsSearch>
    </MainPageWrapper>
  )
}
