import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { SupplementCategoryItem } from '@/features/product/components/SupplementComponnts/SupplementCategoryItem/SupplementCategoryItem'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { APP_ROUTES } from '@/routes'
import { supplementCategoryService } from '@/services/supplement-category.service'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

export default async function Page() {
  const t = await getTranslations()

  const categories = await supplementCategoryService.getSupplementCategories({
    params: {
      limit: 0,
      depth: 1,
      locale: 'all',

      fallbackLocale: false,
    },
    options: {
      cache: 'default',
      next: {
        revalidate: 300,
      },
    },
  })
  const cookieStore = await cookies()
  const isNativeEnvironment = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  return (
    <MainPageWrapper withSubheader={false}>
      <div className="mx-auto flex h-full w-full bg-custom-background-hover">
        <div className="h-full w-full px-4 py-6">
          <div className="space-y-2">
            <PreviousPageButton
              customURL={APP_ROUTES.PRODUCTS.children?.SUPPLEMENTS.path}
            ></PreviousPageButton>
            <div className="typo-heading-7 text-primary">{t('MES-560')}</div>
          </div>

          <div className="mt-4 grid grid-cols-2 gap-1">
            {categories &&
              categories?.docs?.map((category, index) => {
                return (
                  <SupplementCategoryItem
                    key={category.id || index}
                    category={category}
                    isListAll={true}
                    isNativeEnvironment={isNativeEnvironment}
                  ></SupplementCategoryItem>
                )
              })}
          </div>
        </div>
      </div>
    </MainPageWrapper>
  )
}
