import { LivePreviewListener } from '@/components/LivePreviewListener'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { ProductDetail } from '@/features/product/components/ProductComponents/ProductDetail/ProductDetail'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { APP_ROUTES } from '@/routes'
import { medicineService } from '@/services/medicine.service'
import { generateMetaMedicine } from '@/utilities/generateMeta'
import type { Metadata } from 'next'
import { cookies, draftMode } from 'next/headers'
import { cache } from 'react'
type Args = {
  params: Promise<{
    slug?: string
    locale: string
  }>
}

export default async function Medicine({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '', locale = 'vi' } = await paramsPromise
  const url = `${APP_ROUTES.PRODUCTS.children?.MEDICINES.path}/${slug}`
  const medicine = await queryMedicineBySlug({ slug, locale })

  if (!medicine) return <PayloadRedirects url={url} />

  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <ProductDetail medicine={medicine}></ProductDetail>
      <PayloadRedirects disableNotFound url={url} />
      {draft && <LivePreviewListener />}
    </MainPageWrapper>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '', locale = 'vi' } = await paramsPromise
  const medicine = await queryMedicineBySlug({ slug, locale })
  if (medicine) return generateMetaMedicine(medicine)
  return {}
}

const queryMedicineBySlug = cache(async ({ slug, locale }: { slug: string; locale: string }) => {
  const cookieStore = await cookies()
  const token = cookieStore.get(PORTAL_TOKEN_KEY)
  const result = await medicineService.getMedicineBySlugV2({
    slug,
    options: {
      cache: 'no-store',
      next: {},
      headers: {
        Cookie: token ? `${token.name}=${token.value}` : '',
      } as HeadersInit,
    },
    params: {
      locale: locale,
      select: {
        patientGroup: true,
        specification: true,
        categories: true,
        isDietarySupplement: true,
        dietarySupplementCategories: true,
      },
    },
  })

  return result || null
})
