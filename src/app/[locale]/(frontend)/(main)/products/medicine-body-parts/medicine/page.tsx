import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { MedicineBodyPartCategories } from '@/features/product/components/MedicineBodyPartCategories/MedicineBodyPartCategories'
import { MedicineBodyPartsSlide } from '@/features/product/components/MedicineBodyPartsSlide/MedicineBodyPartsSlide'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { APP_ROUTES } from '@/routes'
import { bodyPartService } from '@/services/body-part.service'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

type SearchParams = Promise<{ [key: string]: string | undefined }>

export default async function Page({ searchParams }: { searchParams: SearchParams }) {
  const paramsValue = await searchParams

  const { bodyPartId, backPage } = paramsValue

  const t = await getTranslations()

  const bodyParts = await bodyPartService.getBodyParts({
    params: {
      limit: 0,
      depth: 1,
      // draft: false,
      locale: 'all',
      fallbackLocale: false,
      where: {
        rootBodyPart: { equals: true },
      },
    },
    options: {
      cache: 'no-store',
    },
  })

  const cookieStore = await cookies()
  const isNativeEnvironment = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'

  return (
    <>
      <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
        <div className="h-full w-full px-4 py-6">
          <div className="space-y-2">
            <PreviousPageButton
              customURL={
                backPage === '1'
                  ? APP_ROUTES.PRODUCTS.children?.BODY_PARTS.path
                  : APP_ROUTES.PRODUCTS.children?.MEDICINES.path
              }
              isNative={isNativeEnvironment}
            ></PreviousPageButton>
            <div className="typo-heading-7 text-primary">{t('MES-478')}</div>
          </div>

          <div className="mt-4">
            <MedicineBodyPartsSlide
              bodyParts={bodyParts?.docs ?? []}
              idSelected={bodyPartId ?? ''}
              backPage={backPage}
            ></MedicineBodyPartsSlide>
          </div>
          {bodyPartId && (
            <MedicineBodyPartCategories
              idBody={bodyPartId ?? ''}
              isNativeEnvironment={isNativeEnvironment}
            ></MedicineBodyPartCategories>
          )}
        </div>
      </MainPageWrapper>
    </>
  )
}
