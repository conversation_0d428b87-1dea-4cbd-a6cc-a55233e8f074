import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'

import healthIcon from '@/assets/icons/heal.svg'

import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { APP_ROUTES } from '@/routes'
import { bodyPartService } from '@/services/body-part.service'
import { getTranslations } from 'next-intl/server'
import { BodyPartInfoCard } from '@/features/product/components/MedicineBodyParts/BodyPartInfoCard'

export default async function Page() {
  const t = await getTranslations()

  const bodyParts = await bodyPartService.getBodyParts({
    params: {
      limit: 0,
      depth: 1,
      // draft: false,
      locale: 'all',
      fallbackLocale: false,
      select: {
        heroImage: true,
        name: true,
        id: true,
      },
      where: {
        rootBodyPart: { equals: true },
      },
    },
    options: {
      cache: 'no-store',
    },
  })

  return (
    <MainPageWrapper withSubheader={false}>
      <div className="mx-auto flex h-full w-full bg-custom-background-hover">
        <div className="h-full w-full px-4 py-6">
          <div className="space-y-2">
            <PreviousPageButton
              customURL={APP_ROUTES.PRODUCTS.children?.MEDICINES.path}
            ></PreviousPageButton>
            <div className="typo-heading-7 text-primary">{t('MES-475')}</div>
          </div>

          <div className="mt-4 grid grid-cols-2 gap-1">
            {bodyParts?.docs.map((part) => (
              <BodyPartInfoCard
                key={part.id}
                src={part.heroImage?.['url'] ?? healthIcon}
                alt={part.name}
                titleLocalize={part.name}
                id={part.id}
                backPage="1"
              />
            ))}
          </div>
        </div>
      </div>
    </MainPageWrapper>
  )
}
