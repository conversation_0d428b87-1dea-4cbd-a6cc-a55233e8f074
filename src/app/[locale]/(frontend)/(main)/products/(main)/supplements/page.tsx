import { SupplementsContainer } from '@/features/product/containers/SupplementsContainer/SuplementsContainer'
import { getTranslations } from 'next-intl/server'
type Args = {
  searchParams: Promise<{ [key: string]: string | undefined }>
}
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-127'),
  }
}
export default async function Page({ searchParams }: Args) {
  const searchParamsValues = await searchParams

  return <SupplementsContainer searchParams={searchParamsValues}></SupplementsContainer>
}
