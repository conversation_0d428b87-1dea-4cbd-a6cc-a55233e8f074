import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

export default function Loading() {
  return (
    <div className="space-y-6 p-4">
      {/* Categories section */}
      <div className="grid grid-cols-2 gap-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="flex flex-col items-center rounded-lg bg-white p-3 shadow-sm">
            <Skeleton className="mb-2 h-12 w-12 rounded-full" />
            <Skeleton className="mb-1 h-4 w-20" />
            <Skeleton className="h-3 w-12" />
          </div>
        ))}
      </div>

      {/* "View all" button */}
      <div className="flex justify-center">
        <Skeleton className="h-6 w-24" />
      </div>

      {/* Featured products section */}
      <div className="rounded-lg bg-amber-50 p-4">
        <div className="mb-4 flex items-center">
          <Skeleton className="mr-2 h-6 w-6" />
          <Skeleton className="h-5 w-32" />
        </div>

        <div className="grid grid-cols-2 gap-4">
          {Array.from({ length: 2 }).map((_, index) => (
            <div key={index} className="rounded-lg bg-white p-3">
              <Skeleton className="mb-3 h-24 w-full" />
              <Skeleton className="mb-2 h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ))}
        </div>

        {/* Pagination dots */}
        <div className="mt-4 flex justify-center space-x-1">
          <Skeleton className="h-2 w-2 rounded-full" />
          <Skeleton className="h-2 w-2 rounded-full" />
        </div>
      </div>
    </div>
  )
}
