import { MainLayout } from '@/layouts/MainLayout/MainLayout/MainLayout'
import { setRequestLocale } from 'next-intl/server'
import React from 'react'

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  setRequestLocale(locale)

  return (
    <React.Fragment>
      <MainLayout>{children}</MainLayout>
    </React.Fragment>
  )
}
