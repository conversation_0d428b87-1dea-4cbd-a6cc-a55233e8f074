import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { ExaminationContainer } from '@/features/examination/containers/ExaminationContainer/ExaminationContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import { cookies } from 'next/headers'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('MES-453'),
  }
}
export default async function Page() {
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  return (
    <MainPageWrapper withSubheader={false} className="flex flex-col bg-background-examiantion-form">
      <ExaminationContainer isNativeView={isNative} />
    </MainPageWrapper>
  )
}
