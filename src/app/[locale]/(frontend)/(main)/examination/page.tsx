import { ExaminationFacultiesContainer } from '@/features/examination/containers/ExaminationFacultiesContainer/ExaminationFacultiesContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'

type Args = {
  searchParams: Promise<{ [key: string]: string | undefined }>
}
export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-455'),
  }
}
export default async function Page({}: Args) {
  return (
    <MainPageWrapper withSubheader>
      <ExaminationFacultiesContainer></ExaminationFacultiesContainer>
    </MainPageWrapper>
  )
}
