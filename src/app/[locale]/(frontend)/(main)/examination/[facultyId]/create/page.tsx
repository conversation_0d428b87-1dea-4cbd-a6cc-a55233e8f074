import { CreateExaminationContainer } from '@/features/examination/containers/CreateExaminationContainer/CreateExaminationContainer'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('MES-470'),
  }
}
export default async function Page() {
  return (
    <MainPageWrapper
      withSubheader={false}
      className="flex h-full flex-1 flex-col bg-custom-background-hover"
    >
      <CreateExaminationContainer></CreateExaminationContainer>
    </MainPageWrapper>
  )
}
