import { LOGIN_SESSION_COOKIE_KEY, PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { TransactionStatus } from '@/enums/transaction.enum'
import SubscriptionTransactionAuthWrapper from '@/features/subscription-modify/containers/SubscriptionPaymentTransaction/SubscriptionTransactionAuthWrapper'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { paymentMethodService } from '@/services/payment-method.service'
import { paymentTransactionService } from '@/services/payment-transaction.service'
import dayjs from 'dayjs'
import { getTranslations } from 'next-intl/server'
import { cookies, headers } from 'next/headers'
import { notFound } from 'next/navigation'
import { cache } from 'react'
type Args = {
  params: Promise<{
    id: string
  }>
}
export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('MES-335'),
  }
}
const Page = async ({ params: paramsPromise }: Args) => {
  const { id } = await paramsPromise
  const paymentMethods = await queryPaymentMethods()
  const paymentTransaction = await queryPaymentTransaction(id)
  if (!paymentTransaction) {
    notFound()
  }
  return (
    <MainPageWrapper withSubheader={false} className="flex flex-col bg-custom-background-hover">
      <SubscriptionTransactionAuthWrapper
        paymentMethods={paymentMethods?.docs || []}
        paymentTransaction={paymentTransaction}
      ></SubscriptionTransactionAuthWrapper>
    </MainPageWrapper>
  )
}

export default Page

const queryPaymentMethods = cache(async () => {
  const result = await paymentMethodService.getPaymentMethods({
    options: {
      cache: 'default',
      next: {
        revalidate: 120,
      },
    },
  })

  return result
})

const queryPaymentTransaction = cache(async (transaction: string) => {
  const cookieStore = await cookies()
  const token = cookieStore.get(PORTAL_TOKEN_KEY)?.value
  const sessionToken = cookieStore.get(LOGIN_SESSION_COOKIE_KEY)?.value
  const headersList = await headers()
  const userAgent = headersList.get('user-agent')
  if (!token || !sessionToken) {
    console.error('No token or sessionToken found')
    return null
  }

  const result = await paymentTransactionService.getTransactionsByUser({
    params: {
      where: {
        and: [
          {
            id: {
              equals: transaction,
            },
            status: {
              equals: TransactionStatus.PENDING_PAYMENT,
            },
            createdAt: { greater_than: dayjs().subtract(15, 'minutes').toISOString() },
          },
        ],
      },
      limit: 1,
    },

    options: {
      cache: 'no-store',
      headers: {
        Cookie: `${PORTAL_TOKEN_KEY}=${token}; ${LOGIN_SESSION_COOKIE_KEY}=${sessionToken}`,
        'User-Agent': userAgent || '',
      },
    },
  })

  return result?.docs?.[0] || null
})
