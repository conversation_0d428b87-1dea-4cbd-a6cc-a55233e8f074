import { SUBSCRIPTION_PLANS_EXTENDED } from '@/constants/subsctiption.constant'
import SubscriptionPlanIntroAuthWrapper from '@/features/subscription-modify/containers/SubscriptionPlanIntro/SubscriptionPlanIntroAuthWrapper'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { subscriptionService } from '@/services/subscription.service'
import { getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { cache } from 'react'
type Args = {
  params: Promise<{
    type: string
  }>
}

export async function generateMetadata({ params: paramsPromise }: Args) {
  const t = await getTranslations()
  const { type } = await paramsPromise
  const subscriptionPlan = await querySubscriptionPlanByType({ type })
  const extendedPlanInfo = subscriptionPlan
    ? SUBSCRIPTION_PLANS_EXTENDED[subscriptionPlan?.type]
    : null

  const titleKey = extendedPlanInfo?.title
  const translatedTitle = titleKey ? t(titleKey) : subscriptionPlan?.type

  return {
    title: translatedTitle,
  }
}
const Page = async ({ params: paramsPromise }: Args) => {
  const { type } = await paramsPromise

  // Redirect 404 page if type is not supported
  if (!SUBSCRIPTION_PLANS_EXTENDED.hasOwnProperty(type)) {
    notFound()
  }

  const subscriptionPlan = await querySubscriptionPlanByType({ type })
  if (!subscriptionPlan) {
    notFound()
  }
  return (
    <MainPageWrapper withSubheader={false} className="flex flex-col bg-custom-background-hover">
      <SubscriptionPlanIntroAuthWrapper
        baseSubscriptionPlan={subscriptionPlan}
      ></SubscriptionPlanIntroAuthWrapper>
    </MainPageWrapper>
  )
}

export default Page
const querySubscriptionPlanByType = cache(async ({ type }: { type: string }) => {
  const result = await subscriptionService.getSubscriptionPlans({
    params: {
      limit: 1,
      depth: 1,
      pagination: false,
      where: {
        type: {
          equals: type,
        },
      },
    },
    options: {
      cache: 'default',
      next: {
        revalidate: 120,
      },
    },
  })

  return result?.docs?.[0] || null
})
