import blueBg from '@/assets/images/blue_bg_large.png'
import { PageBannerV2 } from '@/components/PageBanner/PageBannerV2'
import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import { TUTORIAL_NAVIGATION_LIST } from '@/constants/tutorial.constant'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { getTranslations } from 'next-intl/server'
import Image from 'next/image'
import Link from 'next/link'
const Page = async () => {
  const t = await getTranslations()
  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <PageBannerV2
        bannerTitle={{
          text: t('MES-441'),
        }}
        backgroundImg={blueBg}
        backButtonCustom={<PreviousPageButton customURL="/user"></PreviousPageButton>}
        className="h-[102px]"
      ></PageBannerV2>
      <div className="space-y-3 px-4 py-3">
        {TUTORIAL_NAVIGATION_LIST.map((item, index) => {
          return (
            <Link
              href={item.url}
              key={index}
              className="block rounded-lg border border-divider bg-white px-4 py-3"
            >
              <div className="flex items-center gap-x-4">
                <Image
                  src={item.icon}
                  alt="tutorial-icon"
                  width={36}
                  height={36}
                  className="size-9 shrink-0"
                ></Image>
                <div className="space-y-1">
                  <h4 className="typo-body-6 text-primary">{t(item.title)}</h4>
                  <p className="typo-body-9 text-subdued">{t(item.description)}</p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>
    </MainPageWrapper>
  )
}

export default Page
