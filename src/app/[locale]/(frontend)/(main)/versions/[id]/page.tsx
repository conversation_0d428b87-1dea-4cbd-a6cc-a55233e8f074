import { PreviousPageButton } from '@/components/PreviousPageButton/PreviousPageButton'
import RichText from '@/components/RichText'
import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { versionService } from '@/services/version.service'
import { dateToYYYYMMDDHHMM } from '@/utilities/date'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { cache } from 'react'

type Args = {
  params: Promise<{
    id: string
    locale: string
  }>
}

export default async function VersionPage({ params: paramsPromise }: Args) {
  const t = await getTranslations()
  const { id = '' } = await paramsPromise
  const version = await getAppVerion(id)
  const { content, updatedAt, name } = version || {}
  if (!version) return notFound()

  return (
    <MainPageWrapper withSubheader={false} className="bg-custom-background-hover">
      <div className="px-4 pt-4">
        <PreviousPageButton customURL="/" title={t('MES-37')}></PreviousPageButton>
      </div>
      <article className="flex flex-col px-4 py-2">
        <h1 className="typo-heading-7 mb-2 text-primary">
          {t('MES-103')} {name}
        </h1>
        {updatedAt && (
          <time className="typo-body-7 text-subdued">
            {t('MES-102')}: {dateToYYYYMMDDHHMM(new Date(updatedAt))}
          </time>
        )}

        <div className="mt-3 space-y-4 rounded-lg bg-white px-4 py-3">
          {/* Content */}
          {content && (
            <RichText className="mx-auto max-w-[48rem]" data={content} enableGutter={false} />
          )}
        </div>
      </article>
    </MainPageWrapper>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { id = '' } = await paramsPromise
  const t = await getTranslations()
  const version = await getAppVerion(id)
  if (!version) return {}
  return {
    title: t('MES-104') + version?.name,
    description: version?.shortDescription,
  }
}
const getAppVerion = cache(async (id: string) => {
  const result = await versionService.getAppVersion({
    id: id,
    options: {
      cache: 'default',
      next: {
        revalidate: 300,
      },
    },
  })

  return result || null
})
