'use client'
import { IframeContainer } from '@/components/IframeContainer/IframeContainer'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import { useEffect } from 'react'

const Page = () => {
  const { isNative } = useDetectNativeEnvironment()
  const { sendMessage } = useWebViewMessaging()
  useEffect(() => {
    if (isNative) {
      sendMessage(MESSAGE_NATIVE.redirectRegister)
    }
  }, [isNative, sendMessage])
  if (isNative) {
    return <></>
  }
  return <IframeContainer src={`${process.env.NEXT_PUBLIC_PORTAL_URL}/signup`}></IframeContainer>
}

export default Page
