'use client'
import { LoadingPage } from '@/components/ui/Loading/LoadingPage'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { user, status } = useAuthentication()
  const { getAllSearchQueries } = useSearchQuery()
  const { redirect } = getAllSearchQueries()
  const router = useRouter()

  useEffect(() => {
    // Only redirect authenticated users away from auth pages
    if (user || status === 'success') {
      const redirectTo = redirect || '/'
      router.replace(redirectTo as string)
    }
  }, [user, status, redirect, router])

  if (!user && status === 'loading') {
    return <LoadingPage></LoadingPage>
  }
  return <>{children}</>
}

export default Layout
