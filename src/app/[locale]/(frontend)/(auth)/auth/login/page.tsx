'use client'
import { <PERSON><PERSON>eContainer } from '@/components/IframeContainer/IframeContainer'
import { SESSION_EXPIRED_COOKIE_KEY } from '@/constants/global.constant'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import Cookies from 'js-cookie'
import { useEffect } from 'react'

const Page = () => {
  const { getAllSearchQueries } = useSearchQuery()
  const { tutorial } = getAllSearchQueries()
  const { isNative } = useDetectNativeEnvironment()
  const { sendMessage } = useWebViewMessaging()
  const loginPortalURL = tutorial
    ? `${process.env.NEXT_PUBLIC_PORTAL_URL}/login?isDemo=true`
    : `${process.env.NEXT_PUBLIC_PORTAL_URL}/login`
  // Clean up session expired cookie when auth pages load
  useEffect(() => {
    // Always clean up the session expired cookie on auth pages
    setTimeout(() => {
      Cookies.remove(SESSION_EXPIRED_COOKIE_KEY, { path: '/' })
    }, 1000)
  }, [])

  useEffect(() => {
    if (isNative) {
      sendMessage(MESSAGE_NATIVE.redirectLogin)
    }
  }, [isNative, sendMessage])
  if (isNative) {
    return <></>
  }
  return <IframeContainer src={loginPortalURL}></IframeContainer>
}

export default Page
