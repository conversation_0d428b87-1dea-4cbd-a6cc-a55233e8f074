'use client'

import { Error404Icon } from '@/components/Icons/Error404Icon'
import { Button } from '@/components/ui/Button/Button'
import { Home, RefreshCcw } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'

export default function NotFound() {
  const t = useTranslations()
  const router = useRouter()

  const refreshPage = () => {
    window.location.reload()
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-white px-6 py-12 text-center">
      <div className="mb-8">
        <Error404Icon className="mx-auto" />
      </div>

      <h1 className="typo-heading-5 mb-1 text-informative-700">404</h1>
      <h2 className="typo-heading-7 mb-3">{t('MES-610')}</h2>
      <p className="typo-body-7 mb-10 text-subdued">
        {t('MES-611')} <br />
        {t('MES-612')}
      </p>

      <div className="flex w-full max-w-[200px] flex-col gap-3">
        <Button variant="blank" onClick={refreshPage} className="bg-primary-50 text-primary">
          <RefreshCcw className="mr-2 h-5 w-5 shrink-0" /> {t('MES-615')}
        </Button>
        <Button onClick={() => router.push('/')}>
          <Home className="mr-2 h-5 w-5 shrink-0" /> {t('MES-616')}
        </Button>
      </div>
    </div>
  )
}
